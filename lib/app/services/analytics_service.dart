import 'dart:developer';

import 'package:firebase_analytics/firebase_analytics.dart';
import 'package:flutter/foundation.dart';

/// Service for handling Firebase Analytics events and tracking
class AnalyticsService {
  factory AnalyticsService() => _instance;
  AnalyticsService._internal();

  static final AnalyticsService _instance = AnalyticsService._internal();

  late FirebaseAnalytics _analytics;
  late FirebaseAnalyticsObserver _observer;

  /// Initialize Firebase Analytics
  Future<void> initialize() async {
    try {
      _analytics = FirebaseAnalytics.instance;
      _observer = FirebaseAnalyticsObserver(analytics: _analytics);

      // Set default user properties
      await _setDefaultUserProperties();

      log('Firebase Analytics initialized successfully');
    } on Exception catch (e) {
      log('Failed to initialize Firebase Analytics: $e');
    }
  }

  /// Helper method to convert `Map<String, dynamic>` to `Map<String, Object>`
  Map<String, Object>? _convertParameters(Map<String, dynamic>? parameters) {
    if (parameters == null) return null;
    return parameters.map((key, value) => MapEntry(key, value as Object));
  }

  /// Get the analytics observer for navigation tracking
  FirebaseAnalyticsObserver get observer => _observer;

  /// Set default user properties
  Future<void> _setDefaultUserProperties() async {
    try {
      await _analytics.setUserProperty(
        name: 'app_type',
        value: 'portfolio_web',
      );

      await _analytics.setUserProperty(
        name: 'platform',
        value: kIsWeb ? 'web' : 'mobile',
      );

      await _analytics.setUserProperty(
        name: 'user_type',
        value: 'visitor',
      );
    } on Exception catch (e) {
      log('Failed to set user properties: $e');
    }
  }

  /// Track page views
  Future<void> trackPageView({
    required String pageName,
    String? pageClass,
    Map<String, Object>? parameters,
  }) async {
    try {
      await _analytics.logScreenView(
        screenName: pageName,
        screenClass: pageClass ?? pageName,
        parameters: parameters,
      );

      if (kDebugMode) {
        log('Page view tracked: $pageName');
      }
    } on Exception catch (e) {
      log('Failed to track page view: $e');
    }
  }

  /// Track button clicks and interactions
  Future<void> trackButtonClick({
    required String buttonName,
    String? section,
    Map<String, Object>? parameters,
  }) async {
    try {
      final eventParameters = <String, Object>{
        'button_name': buttonName,
        if (section != null) 'section': section,
        ...?parameters,
      };

      await _analytics.logEvent(
        name: 'button_click',
        parameters: eventParameters,
      );

      if (kDebugMode) {
        log('Button click tracked: $buttonName');
      }
    } on Exception catch (e) {
      log('Failed to track button click: $e');
    }
  }

  /// Track project interactions
  Future<void> trackProjectInteraction({
    required String projectName,
    required String action, // 'view', 'click_demo', 'click_github', etc.
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final eventParameters = <String, Object>{
        'project_name': projectName,
        'action': action,
        ...?_convertParameters(parameters),
      };

      await _analytics.logEvent(
        name: 'project_interaction',
        parameters: eventParameters,
      );

      if (kDebugMode) {
        log('Project interaction tracked: $projectName - $action');
      }
    } on Exception catch (e) {
      log('Failed to track project interaction: $e');
    }
  }

  /// Track contact form interactions
  Future<void> trackContactInteraction({
    required String action, // 'form_view', 'form_submit', 'email_click', etc.
    String? method, // 'email', 'linkedin', 'github', etc.
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final eventParameters = <String, Object>{
        'action': action,
        if (method != null) 'contact_method': method,
        ...?_convertParameters(parameters),
      };

      await _analytics.logEvent(
        name: 'contact_interaction',
        parameters: eventParameters,
      );

      if (kDebugMode) {
        log('Contact interaction tracked: $action');
      }
    } on Exception catch (e) {
      log('Failed to track contact interaction: $e');
    }
  }

  /// Track skill/technology interactions
  Future<void> trackSkillInteraction({
    required String skillName,
    required String action, // 'view', 'click', 'hover', etc.
    String? category, // 'frontend', 'backend', 'mobile', etc.
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final eventParameters = <String, Object>{
        'skill_name': skillName,
        'action': action,
        if (category != null) 'skill_category': category,
        ...?_convertParameters(parameters),
      };

      await _analytics.logEvent(
        name: 'skill_interaction',
        parameters: eventParameters,
      );

      if (kDebugMode) {
        log('Skill interaction tracked: $skillName - $action');
      }
    } on Exception catch (e) {
      log('Failed to track skill interaction: $e');
    }
  }

  /// Track CV/Resume downloads
  Future<void> trackCVDownload({
    String? source, // 'header_button', 'contact_section', etc.
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final eventParameters = <String, Object>{
        'download_type': 'cv',
        if (source != null) 'source': source,
        ...?_convertParameters(parameters),
      };

      await _analytics.logEvent(
        name: 'file_download',
        parameters: eventParameters,
      );

      if (kDebugMode) {
        log('CV download tracked from: ${source ?? 'unknown'}');
      }
    } on Exception catch (e) {
      log('Failed to track CV download: $e');
    }
  }

  /// Track social media link clicks
  Future<void> trackSocialMediaClick({
    required String platform, // 'linkedin', 'github', 'twitter', etc.
    String? source, // 'header', 'footer', 'contact_section', etc.
    Map<String, dynamic>? parameters,
  }) async {
    try {
      final eventParameters = <String, Object>{
        'platform': platform,
        if (source != null) 'source': source,
        ...?_convertParameters(parameters),
      };

      await _analytics.logEvent(
        name: 'social_media_click',
        parameters: eventParameters,
      );

      if (kDebugMode) {
        log('Social media click tracked: $platform');
      }
    } on Exception catch (e) {
      log('Failed to track social media click: $e');
    }
  }

  /// Track custom events
  Future<void> trackCustomEvent({
    required String eventName,
    Map<String, dynamic>? parameters,
  }) async {
    try {
      await _analytics.logEvent(
        name: eventName,
        parameters: _convertParameters(parameters),
      );

      if (kDebugMode) {
        log('Custom event tracked: $eventName');
      }
    } on Exception catch (e) {
      log('Failed to track custom event: $e');
    }
  }

  /// Set user ID for tracking
  Future<void> setUserId(String userId) async {
    try {
      await _analytics.setUserId(id: userId);

      if (kDebugMode) {
        log('User ID set: $userId');
      }
    } on Exception catch (e) {
      log('Failed to set user ID: $e');
    }
  }

  /// Reset analytics data (useful for testing)
  Future<void> resetAnalyticsData() async {
    try {
      await _analytics.resetAnalyticsData();

      if (kDebugMode) {
        log('Analytics data reset');
      }
    } on Exception catch (e) {
      log('Failed to reset analytics data: $e');
    }
  }
}
