# Naukri.com Credentials
NAUKRI_USERNAME=<EMAIL>
NAUKRI_PASSWORD=your_password

# Application Preferences
# Comma-separated list of job titles to search for
JOB_TITLES=Software Engineer,Frontend Developer,Backend Developer
# Comma-separated list of skills to filter by
SKILLS=JavaScript,TypeScript,React,Node.js
# Comma-separated list of locations to filter by
LOCATIONS=Bangalore,Mumbai,Remote
# Minimum experience in years
MIN_EXPERIENCE=2
# Maximum experience in years
MAX_EXPERIENCE=8
# Minimum salary in lakhs per annum
MIN_SALARY=10
# Maximum salary in lakhs per annum
MAX_SALARY=30

# Database Configuration
DATABASE_URL=sqlite:./data/applications.db

# Notification Settings
# Email notification settings (using nodemailer)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password
NOTIFICATION_EMAIL=<EMAIL>

# Scheduler Settings
# Cron expression for job application schedule (default: every day at 10:00 AM)
SCHEDULE_CRON="0 10 * * *"
# Maximum number of applications to submit per day
MAX_APPLICATIONS_PER_DAY=10
# Minimum delay between applications in seconds
MIN_DELAY_BETWEEN_APPLICATIONS=300
# Maximum delay between applications in seconds
MAX_DELAY_BETWEEN_APPLICATIONS=900

# Automation Settings
# Whether to run in headless mode (true/false)
HEADLESS=true
# Whether to take screenshots of each step (true/false)
TAKE_SCREENSHOTS=true
# Whether to save application logs (true/false)
SAVE_LOGS=true
# Log level (debug, info, warn, error)
LOG_LEVEL=info
