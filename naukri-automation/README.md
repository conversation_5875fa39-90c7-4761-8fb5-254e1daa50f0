# Naukri.com Job Application Automation Framework

A comprehensive Playwright automation framework for Naukri.com job applications. This solution is production-ready, ethically sound, and technically optimized.

## Features

- **Authentication**: Secure login with session management
- **Job Search**: Advanced filtering by job title, skills, location, etc.
- **Application Submission**: Handles different application workflows
- **Resilience**: Robust error handling and retry mechanisms
- **Performance**: Optimized for speed and resource usage
- **Scheduling**: Automated job application with configurable schedules
- **Tracking**: Database for application history and status
- **Notifications**: Email or messaging alerts for application status
- **Compliance**: Respects Naukri.com's Terms of Service and rate limits

## Getting Started

### Prerequisites

- Node.js (v16 or later)
- npm or yarn
- TypeScript knowledge
- Basic understanding of Playwright

### Installation

```bash
# Clone the repository
git clone <repository-url>
cd naukri-automation

# Install dependencies
npm install
```

### Configuration

1. Create a `.env` file in the root directory:

```
NAUKRI_USERNAME=<EMAIL>
NAUKRI_PASSWORD=your_password
DATABASE_URL=sqlite:./data/applications.db
NOTIFICATION_EMAIL=<EMAIL>
```

2. Configure your job search preferences in `config/job-preferences.ts`

### Usage

```bash
# Run the automation once
npm run apply

# Start the scheduler
npm run scheduler

# Generate reports
npm run report
```

## Project Structure

```
naukri-automation/
├── config/                 # Configuration files
├── data/                   # Database and data files
├── logs/                   # Log files
├── reports/                # Generated reports
├── src/
│   ├── auth/               # Authentication module
│   ├── database/           # Database operations
│   ├── job-search/         # Job search functionality
│   ├── application/        # Application submission
│   ├── notifications/      # Notification system
│   ├── utils/              # Utility functions
│   ├── scheduler.ts        # Scheduling system
│   └── index.ts            # Main entry point
├── tests/                  # Test files
├── .env                    # Environment variables (not in repo)
├── .env.example            # Example environment variables
├── package.json            # Project dependencies
├── playwright.config.ts    # Playwright configuration
└── tsconfig.json           # TypeScript configuration
```

## Ethical Guidelines

This automation framework is designed to respect Naukri.com's Terms of Service:

- Implements appropriate rate limiting
- Uses proper user-agent identification
- Respects server resources
- Handles data according to privacy regulations

## License

MIT
