import { Page } from '@playwright/test';
import logger from '../utils/logger';
import { retry } from '../utils/retry';
import path from 'path';
import fs from 'fs';

/**
 * Checks if the user is already logged in
 * @param page - Playwright page object
 * @returns boolean indicating if user is logged in
 */
export async function isLoggedIn(page: Page): Promise<boolean> {
  try {
    // Check for elements that are only visible when logged in
    // This could be a profile icon, username display, etc.
    const profileElement = await page.$('div[data-test-id="user-profile-section"]');
    return !!profileElement;
  } catch (error) {
    logger.debug('Error checking login status', { error });
    return false;
  }
}

/**
 * <PERSON>les login with 2FA if encountered
 * @param page - Playwright page object
 */
async function handle2FA(page: Page): Promise<void> {
  try {
    // Check if 2FA is required
    const is2FARequired = await page.isVisible('input[type="otp"], input[placeholder*="OTP"]');
    
    if (is2FARequired) {
      logger.info('2FA detected. Waiting for user input...');
      
      // Wait for the user to manually enter the OTP
      // This is an ethical approach as we're not trying to bypass 2FA
      await page.waitForNavigation({ timeout: 120000 }); // 2 minutes timeout
      
      logger.info('2FA completed successfully');
    }
  } catch (error) {
    logger.error('Error handling 2FA', { error });
    throw new Error('Failed to handle 2FA: ' + error);
  }
}

/**
 * Authenticates the user on Naukri.com
 * @param page - Playwright page object
 */
export async function authenticateUser(page: Page): Promise<void> {
  // Check if we have a stored authentication state
  const authFilePath = path.join(process.cwd(), 'data', 'auth.json');
  const authFileExists = fs.existsSync(authFilePath);
  
  // If auth file exists and is less than 24 hours old, we can try to use it
  if (authFileExists) {
    const stats = fs.statSync(authFilePath);
    const fileAgeHours = (Date.now() - stats.mtimeMs) / (1000 * 60 * 60);
    
    if (fileAgeHours < 24) {
      logger.info('Using existing authentication state');
      await page.context().storageState({ path: authFilePath });
      
      // Navigate to Naukri.com to verify the session
      await page.goto('https://www.naukri.com/mnjuser/homepage');
      
      // Check if we're still logged in
      const loggedIn = await isLoggedIn(page);
      if (loggedIn) {
        logger.info('Successfully authenticated using stored session');
        return;
      }
      
      logger.info('Stored session expired, proceeding with login');
    }
  }
  
  // If we don't have a valid stored session, proceed with login
  await retry(async () => {
    try {
      logger.info('Navigating to Naukri.com login page');
      await page.goto('https://www.naukri.com/nlogin/login');
      
      // Wait for the login form to be visible
      await page.waitForSelector('form#login-form', { timeout: 10000 });
      
      // Fill in the username and password
      logger.info('Filling login credentials');
      await page.fill('input[placeholder="Enter your active Email ID / Username"]', process.env.NAUKRI_USERNAME || '');
      await page.fill('input[placeholder="Enter your password"]', process.env.NAUKRI_PASSWORD || '');
      
      // Take a screenshot if enabled
      if (process.env.TAKE_SCREENSHOTS === 'true') {
        await page.screenshot({ path: path.join(process.cwd(), 'screenshots', 'login-form.png') });
      }
      
      // Click the login button
      logger.info('Submitting login form');
      await page.click('button[type="submit"]');
      
      // Handle 2FA if required
      await handle2FA(page);
      
      // Wait for navigation to complete
      await page.waitForNavigation({ waitUntil: 'networkidle' });
      
      // Verify that we're logged in
      const loggedIn = await isLoggedIn(page);
      if (!loggedIn) {
        throw new Error('Login failed: User is not logged in after form submission');
      }
      
      logger.info('Login successful');
      
      // Save the authentication state
      await page.context().storageState({ path: authFilePath });
      logger.info('Authentication state saved');
    } catch (error) {
      logger.error('Login failed', { error });
      throw error;
    }
  }, {
    retries: 3,
    minTimeout: 1000,
    maxTimeout: 5000,
    onRetry: (error, attempt) => {
      logger.warn(`Login attempt ${attempt} failed, retrying...`, { error });
    },
  });
}
