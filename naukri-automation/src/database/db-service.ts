import Database from 'better-sqlite3';
import path from 'path';
import fs from 'fs';
import logger from '../utils/logger';
import { JobApplication, ApplicationStatus, schema, rowToJobApplication, jobApplicationToRow } from './models';

/**
 * Database service for managing job applications
 */
export class DatabaseService {
  private db: Database.Database;
  
  /**
   * Creates a new DatabaseService instance
   * @param dbPath - Path to the database file
   */
  constructor(dbPath?: string) {
    // Create data directory if it doesn't exist
    const dataDir = path.join(process.cwd(), 'data');
    if (!fs.existsSync(dataDir)) {
      fs.mkdirSync(dataDir, { recursive: true });
    }
    
    // Use provided path or default
    const dbFilePath = dbPath || path.join(dataDir, 'applications.db');
    
    // Initialize database
    this.db = new Database(dbFilePath);
    
    // Enable foreign keys
    this.db.pragma('foreign_keys = ON');
    
    // Initialize database schema
    this.initializeSchema();
    
    logger.info('Database initialized', { path: dbFilePath });
  }
  
  /**
   * Initializes the database schema
   */
  private initializeSchema(): void {
    try {
      // Create tables
      this.db.exec(schema.jobApplications);
      this.db.exec(schema.jobApplicationsIndex);
      
      logger.debug('Database schema initialized');
    } catch (error) {
      logger.error('Failed to initialize database schema', { error });
      throw error;
    }
  }
  
  /**
   * Saves a job application to the database
   * @param application - Job application to save
   * @returns Saved job application with ID
   */
  saveJobApplication(application: JobApplication): JobApplication {
    try {
      const row = jobApplicationToRow(application);
      
      // Check if the job already exists
      const existingJob = this.db.prepare('SELECT * FROM job_applications WHERE url = ?').get(application.url);
      
      if (existingJob) {
        // Update existing job
        const updateStmt = this.db.prepare(`
          UPDATE job_applications SET
            title = ?, company = ?, location = ?, experience = ?, salary = ?,
            description = ?, posted_date = ?, application_type = ?, status = ?,
            found_at = ?, applied_at = ?, error_message = ?, notes = ?, reviewed = ?
          WHERE url = ?
        `);
        
        updateStmt.run(
          row.title, row.company, row.location, row.experience, row.salary,
          row.description, row.posted_date, row.application_type, row.status,
          row.found_at, row.applied_at, row.error_message, row.notes, row.reviewed,
          row.url
        );
        
        logger.debug('Updated job application', { url: application.url });
        
        // Return updated job
        return {
          ...application,
          id: existingJob.id,
        };
      } else {
        // Insert new job
        const insertStmt = this.db.prepare(`
          INSERT INTO job_applications (
            title, company, location, experience, salary, description, posted_date,
            url, application_type, status, found_at, applied_at, error_message, notes, reviewed
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);
        
        const result = insertStmt.run(
          row.title, row.company, row.location, row.experience, row.salary,
          row.description, row.posted_date, row.url, row.application_type, row.status,
          row.found_at, row.applied_at, row.error_message, row.notes, row.reviewed
        );
        
        logger.debug('Inserted new job application', { id: result.lastInsertRowid });
        
        // Return inserted job with ID
        return {
          ...application,
          id: result.lastInsertRowid as number,
        };
      }
    } catch (error) {
      logger.error('Failed to save job application', { error, url: application.url });
      throw error;
    }
  }
  
  /**
   * Updates the status of a job application
   * @param url - URL of the job application
   * @param status - New status
   * @param errorMessage - Error message if status is FAILED
   * @param notes - Additional notes
   * @returns Updated job application
   */
  updateJobStatus(
    url: string,
    status: ApplicationStatus,
    errorMessage?: string,
    notes?: string
  ): JobApplication | null {
    try {
      // Get the current job application
      const job = this.getJobByUrl(url);
      
      if (!job) {
        logger.warn('Job not found for status update', { url });
        return null;
      }
      
      // Update the status
      const updateStmt = this.db.prepare(`
        UPDATE job_applications SET
          status = ?,
          applied_at = ?,
          error_message = ?,
          notes = ?
        WHERE url = ?
      `);
      
      const appliedAt = status === ApplicationStatus.APPLIED ? new Date().toISOString() : job.appliedAt?.toISOString();
      
      updateStmt.run(
        status,
        appliedAt,
        errorMessage || job.errorMessage,
        notes || job.notes,
        url
      );
      
      logger.debug('Updated job status', { url, status });
      
      // Return updated job
      return this.getJobByUrl(url);
    } catch (error) {
      logger.error('Failed to update job status', { error, url });
      throw error;
    }
  }
  
  /**
   * Gets a job application by URL
   * @param url - URL of the job application
   * @returns Job application or null if not found
   */
  getJobByUrl(url: string): JobApplication | null {
    try {
      const row = this.db.prepare('SELECT * FROM job_applications WHERE url = ?').get(url);
      
      if (!row) {
        return null;
      }
      
      return rowToJobApplication(row);
    } catch (error) {
      logger.error('Failed to get job by URL', { error, url });
      throw error;
    }
  }
  
  /**
   * Gets all job applications with the specified status
   * @param status - Status to filter by
   * @returns Array of job applications
   */
  getJobsByStatus(status: ApplicationStatus): JobApplication[] {
    try {
      const rows = this.db.prepare('SELECT * FROM job_applications WHERE status = ? ORDER BY found_at DESC').all(status);
      
      return rows.map(rowToJobApplication);
    } catch (error) {
      logger.error('Failed to get jobs by status', { error, status });
      throw error;
    }
  }
  
  /**
   * Gets all job applications
   * @returns Array of job applications
   */
  getAllJobs(): JobApplication[] {
    try {
      const rows = this.db.prepare('SELECT * FROM job_applications ORDER BY found_at DESC').all();
      
      return rows.map(rowToJobApplication);
    } catch (error) {
      logger.error('Failed to get all jobs', { error });
      throw error;
    }
  }
  
  /**
   * Closes the database connection
   */
  close(): void {
    this.db.close();
    logger.debug('Database connection closed');
  }
}
