/**
 * Application status enum
 */
export enum ApplicationStatus {
  /** Job has been found but not yet applied to */
  FOUND = 'FOUND',
  /** Application is in progress */
  IN_PROGRESS = 'IN_PROGRESS',
  /** Application has been successfully submitted */
  APPLIED = 'APPLIED',
  /** Application failed */
  FAILED = 'FAILED',
  /** Job is not suitable for application */
  NOT_SUITABLE = 'NOT_SUITABLE',
  /** Already applied to this job */
  ALREADY_APPLIED = 'ALREADY_APPLIED',
  /** Application requires manual intervention */
  MANUAL_INTERVENTION = 'MANUAL_INTERVENTION',
}

/**
 * Job application record
 */
export interface JobApplication {
  /** Unique identifier for the job application */
  id?: number;
  /** Job title */
  title: string;
  /** Company name */
  company: string;
  /** Job location */
  location: string;
  /** Experience required */
  experience: string;
  /** Salary range */
  salary: string;
  /** Job description */
  description: string;
  /** Job posting date */
  postedDate: string;
  /** Job URL */
  url: string;
  /** Application type (Quick Apply, etc.) */
  applicationType: string;
  /** Current status of the application */
  status: ApplicationStatus;
  /** Timestamp when the job was found */
  foundAt: Date;
  /** Timestamp when the application was submitted */
  appliedAt?: Date;
  /** Error message if application failed */
  errorMessage?: string;
  /** Notes about the application */
  notes?: string;
  /** Whether the application has been reviewed */
  reviewed: boolean;
}

/**
 * Database schema
 */
export const schema = {
  jobApplications: `
    CREATE TABLE IF NOT EXISTS job_applications (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      company TEXT NOT NULL,
      location TEXT NOT NULL,
      experience TEXT,
      salary TEXT,
      description TEXT,
      posted_date TEXT,
      url TEXT NOT NULL UNIQUE,
      application_type TEXT,
      status TEXT NOT NULL,
      found_at DATETIME NOT NULL,
      applied_at DATETIME,
      error_message TEXT,
      notes TEXT,
      reviewed INTEGER NOT NULL DEFAULT 0
    )
  `,
  jobApplicationsIndex: `
    CREATE INDEX IF NOT EXISTS idx_job_applications_status ON job_applications(status)
  `,
};

/**
 * Converts a database row to a JobApplication object
 * @param row - Database row
 * @returns JobApplication object
 */
export function rowToJobApplication(row: any): JobApplication {
  return {
    id: row.id,
    title: row.title,
    company: row.company,
    location: row.location,
    experience: row.experience,
    salary: row.salary,
    description: row.description,
    postedDate: row.posted_date,
    url: row.url,
    applicationType: row.application_type,
    status: row.status as ApplicationStatus,
    foundAt: new Date(row.found_at),
    appliedAt: row.applied_at ? new Date(row.applied_at) : undefined,
    errorMessage: row.error_message,
    notes: row.notes,
    reviewed: Boolean(row.reviewed),
  };
}

/**
 * Converts a JobApplication object to a database row
 * @param application - JobApplication object
 * @returns Database row
 */
export function jobApplicationToRow(application: JobApplication): any {
  return {
    title: application.title,
    company: application.company,
    location: application.location,
    experience: application.experience,
    salary: application.salary,
    description: application.description,
    posted_date: application.postedDate,
    url: application.url,
    application_type: application.applicationType,
    status: application.status,
    found_at: application.foundAt.toISOString(),
    applied_at: application.appliedAt?.toISOString(),
    error_message: application.errorMessage,
    notes: application.notes,
    reviewed: application.reviewed ? 1 : 0,
  };
}
