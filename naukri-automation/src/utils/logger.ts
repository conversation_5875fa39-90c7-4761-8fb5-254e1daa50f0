import winston from 'winston';
import path from 'path';
import fs from 'fs';
import { format } from 'date-fns';

// Create logs directory if it doesn't exist
const logsDir = path.join(process.cwd(), 'logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// Generate a filename based on the current date
const getLogFileName = () => {
  const date = format(new Date(), 'yyyy-MM-dd');
  return `${date}.log`;
};

// Create a Winston logger instance
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss' }),
    winston.format.errors({ stack: true }),
    winston.format.splat(),
    winston.format.json()
  ),
  defaultMeta: { service: 'naukri-automation' },
  transports: [
    // Write logs to console
    new winston.transports.Console({
      format: winston.format.combine(
        winston.format.colorize(),
        winston.format.printf(({ timestamp, level, message, ...meta }) => {
          return `${timestamp} ${level}: ${message} ${
            Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''
          }`;
        })
      ),
    }),
    // Write logs to file if SAVE_LOGS is enabled
    ...(process.env.SAVE_LOGS === 'true'
      ? [
          new winston.transports.File({
            filename: path.join(logsDir, getLogFileName()),
            format: winston.format.combine(
              winston.format.printf(({ timestamp, level, message, ...meta }) => {
                return `${timestamp} ${level}: ${message} ${
                  Object.keys(meta).length ? JSON.stringify(meta, null, 2) : ''
                }`;
              })
            ),
          }),
        ]
      : []),
  ],
});

export default logger;
