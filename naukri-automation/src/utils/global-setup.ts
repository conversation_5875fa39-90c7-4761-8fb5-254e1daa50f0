import { chromium, FullConfig } from '@playwright/test';
import fs from 'fs';
import path from 'path';
import logger from './logger';
import { authenticateUser } from '../auth/login';

/**
 * Global setup function that runs before all tests
 * - Creates necessary directories
 * - Sets up authentication state
 */
async function globalSetup(config: FullConfig) {
  logger.info('Starting global setup');

  // Create necessary directories
  const dirs = ['data', 'logs', 'reports', 'screenshots'];
  dirs.forEach((dir) => {
    const dirPath = path.join(process.cwd(), dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      logger.info(`Created directory: ${dirPath}`);
    }
  });

  // Set up authentication state if credentials are provided
  if (process.env.NAUKRI_USERNAME && process.env.NAUKRI_PASSWORD) {
    logger.info('Setting up authentication state');
    
    // Launch a browser
    const browser = await chromium.launch({
      headless: process.env.HEADLESS !== 'false',
      slowMo: 100,
    });
    
    // Create a new browser context
    const context = await browser.newContext({
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NaukriAutomation/1.0.0',
    });
    
    // Create a new page
    const page = await context.newPage();
    
    try {
      // Authenticate the user
      await authenticateUser(page);
      
      // Save the authenticated state to a file
      await context.storageState({ path: path.join(process.cwd(), 'data', 'auth.json') });
      logger.info('Authentication state saved successfully');
    } catch (error) {
      logger.error('Failed to authenticate user', { error });
      throw error;
    } finally {
      // Close the browser
      await browser.close();
    }
  } else {
    logger.warn('Naukri credentials not provided. Authentication state will not be set up.');
  }

  logger.info('Global setup completed');
}

export default globalSetup;
