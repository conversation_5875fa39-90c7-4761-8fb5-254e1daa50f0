import pRetry from 'p-retry';
import logger from './logger';

/**
 * Options for the retry function
 */
interface RetryOptions {
  /** Number of retry attempts before failing */
  retries?: number;
  /** Minimum timeout between retries in milliseconds */
  minTimeout?: number;
  /** Maximum timeout between retries in milliseconds */
  maxTimeout?: number;
  /** Factor to increase timeout between retries */
  factor?: number;
  /** Function to call on each retry attempt */
  onRetry?: (error: Error, attempt: number) => void;
}

/**
 * Retries a function with exponential backoff
 * @param fn - The function to retry
 * @param options - Retry options
 * @returns The result of the function
 */
export async function retry<T>(
  fn: () => Promise<T>,
  options: RetryOptions = {}
): Promise<T> {
  const {
    retries = 3,
    minTimeout = 1000,
    maxTimeout = 10000,
    factor = 2,
    onRetry = (error, attempt) => {
      logger.warn(`Retry attempt ${attempt}`, { error: error.message });
    },
  } = options;

  return pRetry(fn, {
    retries,
    minTimeout,
    maxTimeout,
    factor,
    onFailedAttempt: onRetry,
  });
}

/**
 * Waits for a specified amount of time
 * @param ms - Time to wait in milliseconds
 * @returns A promise that resolves after the specified time
 */
export function wait(ms: number): Promise<void> {
  return new Promise((resolve) => setTimeout(resolve, ms));
}

/**
 * Adds jitter to a delay to avoid predictable patterns
 * @param baseDelay - Base delay in milliseconds
 * @param jitterFactor - Factor to determine maximum jitter (0-1)
 * @returns Delay with added jitter
 */
export function addJitter(baseDelay: number, jitterFactor = 0.3): number {
  const maxJitter = baseDelay * jitterFactor;
  const jitter = Math.random() * maxJitter * 2 - maxJitter;
  return Math.max(0, baseDelay + jitter);
}
