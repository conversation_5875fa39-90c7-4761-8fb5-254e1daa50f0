import { Page } from '@playwright/test';
import logger from '../utils/logger';
import { retry, wait, addJitter } from '../utils/retry';
import path from 'path';
import { ApplicationStatus } from '../database/models';

/**
 * Job search parameters
 */
export interface JobSearchParams {
  /** Job titles to search for */
  jobTitles?: string[];
  /** Skills to filter by */
  skills?: string[];
  /** Locations to filter by */
  locations?: string[];
  /** Minimum experience in years */
  minExperience?: number;
  /** Maximum experience in years */
  maxExperience?: number;
  /** Minimum salary in lakhs per annum */
  minSalary?: number;
  /** Maximum salary in lakhs per annum */
  maxSalary?: number;
  /** Companies to include */
  companies?: string[];
  /** Maximum age of job posting in days */
  maxJobAge?: number;
}

/**
 * Job details
 */
export interface JobDetails {
  /** Job title */
  title: string;
  /** Company name */
  company: string;
  /** Job location */
  location: string;
  /** Experience required */
  experience: string;
  /** Salary range */
  salary: string;
  /** Job description */
  description: string;
  /** Job posting date */
  postedDate: string;
  /** Job URL */
  url: string;
  /** Application type (Quick Apply, etc.) */
  applicationType: string;
}

/**
 * Navigates to the job search page
 * @param page - Playwright page object
 */
async function navigateToJobSearch(page: Page): Promise<void> {
  await page.goto('https://www.naukri.com/jobs-in-india');
  await page.waitForLoadState('networkidle');
}

/**
 * Applies job search filters
 * @param page - Playwright page object
 * @param params - Job search parameters
 */
async function applyFilters(page: Page, params: JobSearchParams): Promise<void> {
  logger.info('Applying job search filters', { params });

  // Apply keyword search if job titles are provided
  if (params.jobTitles && params.jobTitles.length > 0) {
    const keyword = params.jobTitles.join(' OR ');
    await page.fill('input[placeholder="Enter keyword / designation / companies"]', keyword);
    logger.debug(`Applied keyword filter: ${keyword}`);
  }

  // Apply location filter if locations are provided
  if (params.locations && params.locations.length > 0) {
    await page.click('input[placeholder="Enter location"]');
    for (const location of params.locations) {
      await page.fill('input[placeholder="Enter location"]', location);
      await wait(1000);

      // Try to select from the dropdown
      const locationOption = await page.$(
        `div[class*="suggestor-option"]:has-text("${location}")`
      );
      if (locationOption) {
        await locationOption.click();
        logger.debug(`Selected location: ${location}`);
      }
    }
    // Click outside to close the dropdown
    await page.click('body', { position: { x: 0, y: 0 } });
  }

  // Apply experience filter if min/max experience is provided
  if (params.minExperience !== undefined || params.maxExperience !== undefined) {
    // Click on experience dropdown
    await page.click('div[data-test-id="experience-filter"]');

    // Select min experience
    if (params.minExperience !== undefined) {
      await page.selectOption('select[data-test-id="exp-min-select"]',
        params.minExperience.toString());
      logger.debug(`Set minimum experience: ${params.minExperience} years`);
    }

    // Select max experience
    if (params.maxExperience !== undefined) {
      await page.selectOption('select[data-test-id="exp-max-select"]',
        params.maxExperience.toString());
      logger.debug(`Set maximum experience: ${params.maxExperience} years`);
    }

    // Apply experience filter
    await page.click('button[data-test-id="exp-apply-filter"]');
  }

  // Apply salary filter if min/max salary is provided
  if (params.minSalary !== undefined || params.maxSalary !== undefined) {
    // Click on salary dropdown
    await page.click('div[data-test-id="salary-filter"]');

    // Select min salary
    if (params.minSalary !== undefined) {
      await page.selectOption('select[data-test-id="sal-min-select"]',
        params.minSalary.toString());
      logger.debug(`Set minimum salary: ${params.minSalary} lakhs`);
    }

    // Select max salary
    if (params.maxSalary !== undefined) {
      await page.selectOption('select[data-test-id="sal-max-select"]',
        params.maxSalary.toString());
      logger.debug(`Set maximum salary: ${params.maxSalary} lakhs`);
    }

    // Apply salary filter
    await page.click('button[data-test-id="sal-apply-filter"]');
  }

  // Apply job freshness filter if maxJobAge is provided
  if (params.maxJobAge !== undefined) {
    // Click on freshness dropdown
    await page.click('div[data-test-id="freshness-filter"]');

    // Select appropriate option based on maxJobAge
    let freshnessOption = '';
    if (params.maxJobAge <= 1) {
      freshnessOption = 'Last 24 hours';
    } else if (params.maxJobAge <= 3) {
      freshnessOption = 'Last 3 days';
    } else if (params.maxJobAge <= 7) {
      freshnessOption = 'Last 7 days';
    } else if (params.maxJobAge <= 30) {
      freshnessOption = 'Last 30 days';
    }

    if (freshnessOption) {
      await page.click(`label:has-text("${freshnessOption}")`);
      logger.debug(`Set job freshness: ${freshnessOption}`);
    }

    // Apply freshness filter
    await page.click('button[data-test-id="freshness-apply-filter"]');
  }

  // Take a screenshot of the applied filters if enabled
  if (process.env.TAKE_SCREENSHOTS === 'true') {
    await page.screenshot({
      path: path.join(process.cwd(), 'screenshots', 'applied-filters.png')
    });
  }

  // Click search button to apply all filters
  await page.click('button[data-test-id="search-button"]');
  await page.waitForLoadState('networkidle');

  logger.info('All filters applied successfully');
}

/**
 * Extracts job details from a job card
 * @param page - Playwright page object
 * @param jobCard - Job card element
 * @returns Job details
 */
async function extractJobDetails(page: Page, jobCard: any): Promise<JobDetails> {
  try {
    // Extract job title
    const titleElement = await jobCard.$('a.title');
    const title = await titleElement?.textContent() || 'Unknown Title';

    // Extract job URL
    const url = await titleElement?.getAttribute('href') || '';

    // Extract company name
    const company = await jobCard.$eval('a.subTitle', (el) => el.textContent || 'Unknown Company')
      .catch(() => 'Unknown Company');

    // Extract job location
    const location = await jobCard.$eval('span[title="location"]', (el) => el.textContent || 'Unknown Location')
      .catch(() => 'Unknown Location');

    // Extract experience required
    const experience = await jobCard.$eval('span[title="experience"]', (el) => el.textContent || 'Not specified')
      .catch(() => 'Not specified');

    // Extract salary
    const salary = await jobCard.$eval('span[title="salary"]', (el) => el.textContent || 'Not disclosed')
      .catch(() => 'Not disclosed');

    // Extract job posting date
    const postedDate = await jobCard.$eval('span.postedDate', (el) => el.textContent || 'Unknown')
      .catch(() => 'Unknown');

    // Extract application type (Quick Apply, etc.)
    const applicationType = await jobCard.$eval('span.applyBtn', (el) => el.textContent || 'Apply')
      .catch(() => 'Apply');

    // For job description, we need to click on the job card and extract from the detail view
    // This is a placeholder - the actual implementation would depend on Naukri.com's UI
    const description = 'Click to view full description';

    return {
      title,
      company,
      location,
      experience,
      salary,
      description,
      postedDate,
      url: url.startsWith('http') ? url : `https://www.naukri.com${url}`,
      applicationType,
    };
  } catch (error) {
    logger.error('Error extracting job details', { error });
    // Return a minimal job object with available information
    return {
      title: await jobCard.$eval('a.title', (el) => el.textContent || 'Unknown Title')
        .catch(() => 'Unknown Title'),
      company: 'Error extracting details',
      location: 'Unknown',
      experience: 'Unknown',
      salary: 'Unknown',
      description: 'Error extracting details',
      postedDate: 'Unknown',
      url: await jobCard.$eval('a.title', (el) => el.getAttribute('href') || '')
        .catch(() => ''),
      applicationType: 'Unknown',
    };
  }
}

/**
 * Handles pagination for job search results
 * @param page - Playwright page object
 * @param maxPages - Maximum number of pages to process
 * @param processJobCard - Function to process each job card
 */
async function handlePagination(
  page: Page,
  maxPages: number,
  processJobCard: (jobCard: any, jobDetails: JobDetails) => Promise<void>
): Promise<void> {
  let currentPage = 1;
  let hasNextPage = true;

  while (hasNextPage && currentPage <= maxPages) {
    logger.info(`Processing page ${currentPage} of job search results`);

    // Wait for job cards to load
    await page.waitForSelector('article.jobTuple', { timeout: 10000 });

    // Take a screenshot of the search results if enabled
    if (process.env.TAKE_SCREENSHOTS === 'true') {
      await page.screenshot({
        path: path.join(process.cwd(), 'screenshots', `search-results-page-${currentPage}.png`)
      });
    }

    // Get all job cards on the current page
    const jobCards = await page.$$('article.jobTuple');
    logger.info(`Found ${jobCards.length} job cards on page ${currentPage}`);

    // Process each job card
    for (let i = 0; i < jobCards.length; i++) {
      const jobCard = jobCards[i];

      // Extract job details
      const jobDetails = await extractJobDetails(page, jobCard);

      // Process the job card with the provided function
      await processJobCard(jobCard, jobDetails);

      // Add a small delay between processing job cards to avoid rate limiting
      await wait(addJitter(500, 0.5));
    }

    // Check if there's a next page
    const nextPageButton = await page.$('a[title="Next"]');
    hasNextPage = !!nextPageButton;

    if (hasNextPage && currentPage < maxPages) {
      // Click the next page button
      await nextPageButton?.click();
      await page.waitForLoadState('networkidle');
      currentPage++;

      // Add a delay between page navigations to avoid rate limiting
      await wait(addJitter(2000, 0.5));
    } else {
      hasNextPage = false;
    }
  }

  logger.info(`Completed processing ${currentPage} pages of job search results`);
}

/**
 * Searches for jobs on Naukri.com based on the provided parameters
 * @param page - Playwright page object
 * @param params - Job search parameters
 * @param maxPages - Maximum number of pages to process
 * @param processJob - Function to process each job
 * @returns Array of processed job details
 */
export async function searchJobs(
  page: Page,
  params: JobSearchParams,
  maxPages: number = 5,
  processJob?: (jobDetails: JobDetails) => Promise<ApplicationStatus>
): Promise<JobDetails[]> {
  const processedJobs: JobDetails[] = [];

  await retry(async () => {
    try {
      // Navigate to the job search page
      await navigateToJobSearch(page);

      // Apply filters
      await applyFilters(page, params);

      // Process job cards
      await handlePagination(page, maxPages, async (jobCard, jobDetails) => {
        // Add job to processed jobs array
        processedJobs.push(jobDetails);

        // If a process function is provided, call it
        if (processJob) {
          const status = await processJob(jobDetails);
          logger.info(`Processed job: ${jobDetails.title} at ${jobDetails.company} - Status: ${status}`);
        }
      });

      logger.info(`Successfully processed ${processedJobs.length} jobs`);
    } catch (error) {
      logger.error('Error searching for jobs', { error });
      throw error;
    }
  }, {
    retries: 2,
    minTimeout: 1000,
    maxTimeout: 5000,
    onRetry: (error, attempt) => {
      logger.warn(`Job search attempt ${attempt} failed, retrying...`, { error });
    },
  });

  return processedJobs;
}
