import { defineConfig, devices } from '@playwright/test';
import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

/**
 * See https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  // Directory where tests are located
  testDir: './tests',
  
  // Maximum time one test can run for
  timeout: 60000,
  
  // Run tests in files in parallel
  fullyParallel: false,
  
  // Fail the build on CI if you accidentally left test.only in the source code
  forbidOnly: !!process.env.CI,
  
  // Retry on CI only
  retries: process.env.CI ? 2 : 0,
  
  // Opt out of parallel tests on CI
  workers: process.env.CI ? 1 : 1,
  
  // Reporter to use
  reporter: [
    ['html', { open: 'never' }],
    ['list']
  ],
  
  // Shared settings for all the projects below
  use: {
    // Base URL to use in actions like `await page.goto('/')`
    baseURL: 'https://www.naukri.com',
    
    // Collect trace when retrying the failed test
    trace: 'on-first-retry',
    
    // Record video only when retrying a test for the first time
    video: 'on-first-retry',
    
    // Record screenshots only when retrying a test for the first time
    screenshot: 'only-on-failure',
    
    // Set a realistic viewport for desktop browsing
    viewport: { width: 1280, height: 720 },
    
    // Set a custom user agent that identifies as a real browser but includes automation info
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/122.0.0.0 Safari/537.36 NaukriAutomation/1.0.0',
    
    // Realistic browser behavior
    ignoreHTTPSErrors: true,
    
    // Emulate desktop device
    ...devices['Desktop Chrome'],
    
    // Slow down Playwright operations to avoid detection
    launchOptions: {
      slowMo: 100,
    },
    
    // Automatically accept dialogs (but log them)
    acceptDownloads: true,
  },
  
  // Configure projects for different browsers
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    
    // Uncomment to enable Firefox and WebKit testing
    // {
    //   name: 'firefox',
    //   use: { ...devices['Desktop Firefox'] },
    // },
    // {
    //   name: 'webkit',
    //   use: { ...devices['Desktop Safari'] },
    // },
  ],
  
  // Folder for test artifacts like screenshots, videos, traces, etc.
  outputDir: 'test-results',
  
  // Global setup to run before all tests
  globalSetup: './src/utils/global-setup.ts',
  
  // Global teardown to run after all tests
  globalTeardown: './src/utils/global-teardown.ts',
});
