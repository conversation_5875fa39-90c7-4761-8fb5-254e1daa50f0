{"name": "naukri-automation", "version": "1.0.0", "description": "Playwright automation framework for Naukri.com job applications", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "npm run build && node dist/index.js", "apply": "npm run build && node dist/index.js --apply", "scheduler": "npm run build && node dist/scheduler.js", "report": "npm run build && node dist/reports/generate.js", "test": "playwright test", "test:headed": "playwright test --headed", "lint": "eslint . --ext .ts", "format": "prettier --write \"**/*.{ts,js,json,md}\"", "prepare": "husky install"}, "keywords": ["playwright", "automation", "<PERSON><PERSON><PERSON>", "job-application"], "author": "", "license": "MIT", "dependencies": {"@playwright/test": "^1.42.1", "better-sqlite3": "^9.4.3", "chalk": "^4.1.2", "commander": "^12.0.0", "date-fns": "^3.3.1", "dotenv": "^16.4.5", "knex": "^3.1.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.10", "p-retry": "^4.6.2", "winston": "^3.11.0", "zod": "^3.22.4"}, "devDependencies": {"@types/better-sqlite3": "^7.6.9", "@types/node": "^20.11.24", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "@typescript-eslint/eslint-plugin": "^7.1.0", "@typescript-eslint/parser": "^7.1.0", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.0.11", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "typescript": "^5.3.3"}, "lint-staged": {"*.ts": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}